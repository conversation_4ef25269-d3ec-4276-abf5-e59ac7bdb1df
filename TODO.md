# TODO - Discord Bybit Signal Monitor

## ✅ Ukończone

- [x] Podstawowa struktura bota Discord
- [x] Parsowanie sygnałów z regex
- [x] Integracja z API Bybit
- [x] Baza danych SQLite
- [x] Monitoring cen w czasie rzeczywistym
- [x] Automatyczne zamykanie pozycji (TP/SL/timeout)
- [x] Komenda statystyk
- [x] Naprawiono problemy z zależnościami (urllib3, discord.py)
- [x] Utworzono pliki konfiguracyjne (.env.example, requirements.txt)
- [x] Dokumentacja (README.md)
- [x] Poprawiono regex do elastycznego parsowania sygnałów
- [x] Dodano test parsowania sygnałów (test_signal_parser.py)
- [x] Naprawiono logikę normalizacji par tradingowych
- [x] Dodano lepsze komentarze i dokumentację w kodzie
- [x] Dodano lepszą obsługę błędów i komunikaty
- [x] Utworzono Dashboard Web z Flask
- [x] Dodano wykresy interaktywne (Chart.js)
- [x] Implementowano real-time updates (WebSocket)
- [x] Dodano filtrowanie i eksport danych
- [x] Utworzono skrypt importu historycznych sygnałów
- [x] Dodano generator przykładowych danych
- [x] Integracja bot-dashboard przez webhook
- [x] Naprawiono deprecation warning SQLite3 (Python 3.12+)
- [x] Poprawiono parsowanie timestampów z timezone
- [x] Dodano type hints i lepsze error handling
- [x] **MAJOR UPDATE**: Znacznie ulepszono dashboard z zaawansowanymi metrykami
- [x] Dodano filtry czasowe (24h, 7 dni, 30 dni)
- [x] Implementowano zaawansowane metryki wydajności (Sharpe Ratio, Max Drawdown, Profit Factor)
- [x] Dodano nowe wykresy (rozkład PnL, wydajność per para)
- [x] Utworzono analizę per timeframe
- [x] Dodano obsługę błędów JavaScript i lepsze UX
- [x] Rozszerzono API o nowe endpointy (/api/performance-metrics, /api/pnl-distribution)
- [x] **MAJOR UPDATE**: Znacznie ulepszono system parsowania sygnałów Discord
- [x] Dodano zaawansowane regex patterns dla różnych formatów sygnałów
- [x] Implementowano obsługę wielokrotnych TP/SL
- [x] Dodano walidację logiki sygnałów (sprawdzanie TP/SL względem Entry)
- [x] Ulepszono normalizację par tradingowych (obsługa różnych quote currencies)
- [x] Dodano retry mechanizm dla API Bybit z exponential backoff
- [x] Implementowano logowanie do pliku (signal_monitor.log)
- [x] Dodano obsługę formatów z emoji i różnymi separatorami
- [x] Rozszerzono obsługę LONG/SHORT jako synonimów BUY/SELL
- [x] Dodano fallback patterns dla różnych formatów sygnałów
- [x] Ulepszono error handling i debugging
- [x] **NOWE (2025-06-14)**: Dodano obsługę sygnałów od botów z konfiguracją whitelist
- [x] **NOWE (2025-06-14)**: Konfigurowalny czas ważności sygnałów (domyślnie 48h)
- [x] **NOWE (2025-06-14)**: Filtrowanie starych wiadomości przy przetwarzaniu
- [x] **NOWE (2025-06-14)**: Szczegółowe logowanie konfiguracji botów przy starcie
- [x] **NOWE (2025-06-14)**: Aktualizacja importu historii z obsługą nowych ustawień
- [x] **CRITICAL FIX (2025-06-14)**: Naprawiono błędy JSON z wartościami NaN/Infinity w dashboard
- [x] **CRITICAL FIX (2025-06-14)**: Naprawiono problem z timeout - wszystkie sygnały mają teraz 48h ważności
- [x] **MAJOR UPDATE (2025-06-14)**: Implementowano kompleksowy system statusów sygnałów:
  - NEW - Nowy sygnał który jeszcze nie osiągnął poziomu entry
  - ENTRY_HIT - Sygnał który osiągnął poziom entry
  - TP_HIT - Sygnał który osiągnął take profit po entry
  - SL_HIT - Sygnał który osiągnął stop loss po entry
  - EXPIRED - Sygnał który wygasł po 48 godzinach
- [x] **MAJOR UPDATE (2025-06-14)**: Zaktualizowano dashboard do obsługi nowych statusów
- [x] **MAJOR UPDATE (2025-06-14)**: Dodano skrypt migracji bazy danych (migrate_signal_statuses.py)
- [x] **MAJOR UPDATE (2025-06-14)**: Zaktualizowano wszystkie narzędzia diagnostyczne

## 🔄 W trakcie

- [ ] Testowanie z rzeczywistymi sygnałami
- [ ] Dodanie tooltipów i help text w dashboard
- [ ] Optymalizacja responsywności na urządzeniach mobilnych

## 📋 Do zrobienia

### Priorytet wysoki

- [x] Dodać walidację formatu sygnałów (zrealizowane - zaawansowana walidacja)
- [x] Obsługa błędów API Bybit (zrealizowane - retry mechanizm z exponential backoff)
- [x] Logowanie do pliku zamiast tylko konsoli (zrealizowane - signal_monitor.log)
- [ ] Dodać testy jednostkowe
- [ ] Dark mode dla dashboard

### Priorytet średni

- [x] Wsparcie dla różnych formatów sygnałów (zrealizowane - zaawansowane regex patterns)
- [x] Eksport statystyk do CSV/Excel (zrealizowane - eksport CSV)
- [ ] Powiadomienia Discord o zamkniętych pozycjach
- [x] Dashboard web do przeglądania statystyk (zrealizowane z zaawansowanymi funkcjami)
- [ ] Konfiguracja przez plik YAML zamiast .env
- [ ] Heatmapa wydajności (dzień vs para)
- [ ] Eksport raportów PDF
- [ ] Porównanie z benchmarkami (np. Buy & Hold)

### Priorytet niski

- [ ] Wsparcie dla futures i spot trading
- [ ] Integracja z innymi giełdami (Binance, OKX)
- [ ] Analiza techniczna sygnałów
- [ ] Machine learning do predykcji sukcesu sygnałów
- [ ] Backtesting historycznych sygnałów

### Optymalizacje

- [ ] Async/await dla wszystkich operacji I/O
- [ ] Connection pooling dla bazy danych
- [ ] Caching cen z Bybit
- [ ] Kompresja logów
- [ ] Monitoring wydajności

### Bezpieczeństwo

- [ ] Szyfrowanie kluczy API w bazie
- [ ] Rate limiting dla komend bota
- [x] Whitelist użytkowników/ról (zrealizowane - whitelist botów)
- [ ] Audit log wszystkich operacji

## 🐛 Znane problemy

- [x] Brak obsługi sygnałów z wieloma TP/SL (naprawione - obsługa TP1, TP2, TP3)
- [x] Regex może nie rozpoznać wszystkich formatów (naprawione - 4 różne patterns)
- [x] Brak obsługi par z różnymi quote currency (naprawione - obsługa USDT, USDC, BTC, ETH, BNB)
- [x] **CRITICAL**: Błędy JSON z NaN/Infinity w dashboard (naprawione - sanityzacja JSON)
- [x] **CRITICAL**: Problem z timeout - sygnały były zamykane po 1 minucie zamiast 48h (naprawione)

## 🐛 Nowe znane problemy

- [ ] Brak obsługi sygnałów z procentowymi TP/SL (np. TP: +5%)
- [ ] Brak obsługi sygnałów z leverage
- [ ] Brak obsługi sygnałów z różnymi typami orderów (market, limit)
- [ ] Potrzeba lepszej walidacji ID botów w whiteliście
- [ ] Brak możliwości dynamicznej aktualizacji whitelisty bez restartu

## 💡 Pomysły na przyszłość

- [ ] Plugin system dla różnych strategii
- [ ] API REST do zarządzania sygnałami
- [ ] Mobile app do monitorowania
- [ ] Integracja z TradingView
- [ ] Social trading features
