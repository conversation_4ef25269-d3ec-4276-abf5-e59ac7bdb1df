"""
Discord Bybit Signal Monitor

Bot Discord do automatycznego monitorowania sygnałów tradingowych
i śledzenia ich wyników na giełdzie Bybit.

Autor: AI Assistant
Data: 2025-06-14
"""

import os
import re
import sqlite3
import logging
import asyncio
import requests
import time
from datetime import datetime, timedelta, timezone
from typing import Optional, Union, List, Dict, Any
from dotenv import load_dotenv
from discord.ext import commands, tasks
from pybit.unified_trading import HTTP

# Konfiguracja SQLite datetime adapter dla Python 3.12+
def adapt_datetime_iso(val: datetime) -> str:
    """Adapter datetime do formatu ISO dla SQLite."""
    return val.isoformat()

def convert_datetime(val: Union[str, bytes, None]) -> Optional[datetime]:
    """Konwerter datetime z formatu ISO dla SQLite."""
    if isinstance(val, bytes):
        val = val.decode('utf-8')

    if not val or val == 'None':
        return None

    # Upew<PERSON>j się, że val jest stringiem
    if not isinstance(val, str):
        return None

    try:
        # Obsługa różnych formatów timestamp
        if val.endswith('+00:00'):
            return datetime.fromisoformat(val)
        elif 'T' in val:
            return datetime.fromisoformat(val.replace('Z', '+00:00'))
        else:
            # Fallback dla starszych formatów
            try:
                return datetime.strptime(val, '%Y-%m-%d %H:%M:%S.%f').replace(tzinfo=timezone.utc)
            except ValueError:
                return datetime.strptime(val, '%Y-%m-%d %H:%M:%S').replace(tzinfo=timezone.utc)
    except ValueError as e:
        logging.getLogger('signal_monitor').warning(f'Nie można sparsować timestamp: {val} - {e}')
        return None

def parse_timestamp_safely(ts: Union[str, datetime, None]) -> Optional[datetime]:
    """Bezpieczne parsowanie timestamp z różnych formatów."""
    if ts is None:
        return None

    if isinstance(ts, datetime):
        # Upewnij się, że ma timezone
        if ts.tzinfo is None:
            return ts.replace(tzinfo=timezone.utc)
        return ts

    if isinstance(ts, str):
        return convert_datetime(ts)

    return None

# Rejestracja adapterów SQLite
sqlite3.register_adapter(datetime, adapt_datetime_iso)
sqlite3.register_converter("DATETIME", convert_datetime)

# Ładowanie zmiennych środowiskowych z pliku .env
load_dotenv()
DISCORD_TOKEN = os.getenv('DISCORD_TOKEN')
GUILD_ID = os.getenv('DISCORD_GUILD_ID')
CHANNEL_ID = os.getenv('DISCORD_CHANNEL_ID')
BYBIT_API_KEY = os.getenv('BYBIT_API_KEY')
BYBIT_API_SECRET = os.getenv('BYBIT_API_SECRET')
PRICE_CHECK_INTERVAL = int(os.getenv('PRICE_CHECK_INTERVAL_SEC', 60))  # Interwał sprawdzania cen w sekundach
DB_PATH = os.getenv('DB_PATH', 'signals.db')  # Ścieżka do bazy danych SQLite
DASHBOARD_URL = os.getenv('DASHBOARD_URL', 'http://localhost:5000')  # URL dashboard dla powiadomień

# Konfiguracja obsługi wiadomości od botów
ALLOW_BOT_MESSAGES = os.getenv('ALLOW_BOT_MESSAGES', 'false').lower() == 'true'
BOT_WHITELIST_STR = os.getenv('BOT_WHITELIST', '')
BOT_WHITELIST = set(BOT_WHITELIST_STR.split(',')) if BOT_WHITELIST_STR else set()
SIGNAL_VALIDITY_HOURS = int(os.getenv('SIGNAL_VALIDITY_HOURS', 48))  # Ważność sygnału w godzinach

# Konfiguracja timeframe
MIN_TIMEFRAME_MINUTES = int(os.getenv('MIN_TIMEFRAME_MINUTES', 15))  # Minimalny timeframe w minutach
DEFAULT_TIMEFRAME_MULTIPLIER = int(os.getenv('DEFAULT_TIMEFRAME_MULTIPLIER', 60))  # Mnożnik dla krótkich TF (1 -> 60min)

# Walidacja wymaganych zmiennych środowiskowych
required = [DISCORD_TOKEN, CHANNEL_ID, BYBIT_API_KEY, BYBIT_API_SECRET]
if not all(required):
    missing = [name for name, val in [
        ('DISCORD_TOKEN', DISCORD_TOKEN), ('DISCORD_CHANNEL_ID', CHANNEL_ID),
        ('BYBIT_API_KEY', BYBIT_API_KEY), ('BYBIT_API_SECRET', BYBIT_API_SECRET)
    ] if not val]
    raise RuntimeError(f"Brakuje wymaganych zmiennych środowiskowych: {', '.join(missing)}")

# Konfiguracja logowania
def setup_logging():
    """Konfiguruje logowanie do pliku i konsoli."""
    logger = logging.getLogger('signal_monitor')
    logger.setLevel(logging.INFO)

    # Usuń istniejące handlery
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Format logów
    formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')

    # Handler do konsoli
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)
    logger.addHandler(console_handler)

    # Handler do pliku
    try:
        file_handler = logging.FileHandler('signal_monitor.log', encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)
        logger.addHandler(file_handler)
        logger.info("Logowanie do pliku skonfigurowane: signal_monitor.log")
    except Exception as e:
        logger.warning(f"Nie udało się skonfigurować logowania do pliku: {e}")

    return logger

logger = setup_logging()

# Setup SQLite database z obsługą datetime
conn = sqlite3.connect(DB_PATH, detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES)
cursor = conn.cursor()
cursor.execute('''
CREATE TABLE IF NOT EXISTS signals (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    message_id TEXT UNIQUE,
    pair TEXT,
    side TEXT,
    entry REAL,
    tp REAL,
    sl REAL,
    timestamp DATETIME,
    timeframe_min INTEGER,
    status TEXT DEFAULT 'open',
    close_timestamp DATETIME,
    exit_price REAL,
    pnl REAL
)
''')
conn.commit()

# Initialize Bybit client
tb = HTTP(
    testnet=False,
    api_key=BYBIT_API_KEY,
    api_secret=BYBIT_API_SECRET
)

# Prepare Discord Bot with message content intent
import discord
intents = discord.Intents.default()
intents.message_content = True
bot = commands.Bot(command_prefix='!', intents=intents)

def is_bot_allowed(author_id: int) -> bool:
    """Sprawdź czy bot jest dozwolony do wysyłania sygnałów."""
    if not ALLOW_BOT_MESSAGES:
        return False

    # Jeśli whitelist jest pusty, pozwól wszystkim botom
    if not BOT_WHITELIST:
        return True

    # Sprawdź czy ID bota jest na whiteliście
    return str(author_id) in BOT_WHITELIST

def notify_dashboard(event_type: str, data: Dict[str, Any]) -> None:
    """Powiadom dashboard o nowym wydarzeniu."""
    try:
        requests.post(
            f"{DASHBOARD_URL}/api/webhook",
            json={"type": event_type, "data": data},
            timeout=2
        )
    except Exception as e:
        logger.debug(f"Nie udało się powiadomić dashboard: {e}")

def get_price_with_retry(symbol: str, max_retries: int = 3, delay: float = 1.0) -> Optional[float]:
    """Pobierz cenę z API Bybit z mechanizmem retry."""
    for attempt in range(max_retries):
        try:
            resp = tb.get_tickers(category='linear', symbol=symbol)
            if resp and 'result' in resp and 'list' in resp['result'] and resp['result']['list']:
                price = float(resp['result']['list'][0]['lastPrice'])
                return price
            else:
                logger.warning(f"Nieprawidłowa odpowiedź API dla {symbol}: {resp}")

        except Exception as e:
            logger.warning(f"Attempt {attempt + 1}/{max_retries} failed for {symbol}: {e}")

            if attempt < max_retries - 1:
                time.sleep(delay * (2 ** attempt))  # Exponential backoff
            else:
                logger.error(f"Failed to get price for {symbol} after {max_retries} attempts")

    return None

def validate_pair_exists(symbol: str) -> bool:
    """Sprawdź czy para tradingowa istnieje na Bybit."""
    try:
        resp = tb.get_tickers(category='linear', symbol=symbol)
        return resp and 'result' in resp and 'list' in resp['result'] and len(resp['result']['list']) > 0
    except Exception as e:
        logger.debug(f"Error validating pair {symbol}: {e}")
        return False

# Zaawansowane regex patterns do parsowania różnych formatów sygnałów
class SignalPatterns:
    """Klasa zawierająca różne wzorce regex do parsowania sygnałów."""

    # Główny pattern - elastyczny format
    MAIN_PATTERN = re.compile(
        r"(?P<side>BUY|SELL|LONG|SHORT)\s+(?P<pair>[A-Z0-9]+)(?:\.?P|PERP|USD[TC]?)?\s*"
        r".*?"
        r"(?:Entry|Price|@)\s*[-:=\s]*(?P<entry>[0-9]*[.,]?[0-9]+)\s*"
        r".*?"
        r"(?:TP|Take\s*Profit|Target)\s*[-:=\s]*(?P<tp>[0-9]*[.,]?[0-9]+)\s*"
        r".*?"
        r"(?:SL|Stop\s*Loss|Stop)\s*[-:=\s]*(?P<sl>[0-9]*[.,]?[0-9]+)\s*"
        r".*?"
        r"(?:Time(?:frame)?|TF|Duration)[:\s]*(?P<tf>[0-9]+)",
        re.IGNORECASE | re.DOTALL
    )

    # Pattern dla sygnałów z wieloma TP
    MULTI_TP_PATTERN = re.compile(
        r"(?P<side>BUY|SELL|LONG|SHORT)\s+(?P<pair>[A-Z0-9]+)(?:\.?P|PERP|USD[TC]?)?\s*"
        r".*?"
        r"(?:Entry|Price|@)\s*[-:=\s]*(?P<entry>[0-9]*[.,]?[0-9]+)\s*"
        r".*?"
        r"(?:TP|Take\s*Profit|Target)\s*1\s*[-:=\s]*(?P<tp1>[0-9]*[.,]?[0-9]+)\s*"
        r"(?:.*?(?:TP|Take\s*Profit|Target)\s*2\s*[-:=\s]*(?P<tp2>[0-9]*[.,]?[0-9]+))?\s*"
        r"(?:.*?(?:TP|Take\s*Profit|Target)\s*3\s*[-:=\s]*(?P<tp3>[0-9]*[.,]?[0-9]+))?\s*"
        r".*?"
        r"(?:SL|Stop\s*Loss|Stop)\s*[-:=\s]*(?P<sl>[0-9]*[.,]?[0-9]+)\s*"
        r".*?"
        r"(?:Time(?:frame)?|TF|Duration)[:\s]*(?P<tf>[0-9]+)",
        re.IGNORECASE | re.DOTALL
    )

    # Pattern dla prostych sygnałów (bez timeframe)
    SIMPLE_PATTERN = re.compile(
        r"(?P<side>BUY|SELL|LONG|SHORT)\s+(?P<pair>[A-Z0-9]+)(?:\.?P|PERP|USD[TC]?)?\s*"
        r".*?"
        r"(?:Entry|Price|@)\s*[-:=\s]*(?P<entry>[0-9]*[.,]?[0-9]+)\s*"
        r".*?"
        r"(?:TP|Take\s*Profit|Target)\s*[-:=\s]*(?P<tp>[0-9]*[.,]?[0-9]+)\s*"
        r".*?"
        r"(?:SL|Stop\s*Loss|Stop)\s*[-:=\s]*(?P<sl>[0-9]*[.,]?[0-9]+)",
        re.IGNORECASE | re.DOTALL
    )

    # Pattern dla sygnałów z emoji i formatowaniem
    FORMATTED_PATTERN = re.compile(
        r"[🔥💎📈📉⚡🎯]*\s*"
        r"(?P<side>BUY|SELL|LONG|SHORT)\s+(?P<pair>[A-Z0-9]+)(?:\.?P|PERP|USD[TC]?)?\s*"
        r"[🔥💎📈📉⚡🎯]*\s*"
        r".*?"
        r"(?:Entry|Price|@)\s*[-:=\s]*(?P<entry>[0-9]*[.,]?[0-9]+)\s*"
        r".*?"
        r"(?:TP|Take\s*Profit|Target)\s*[-:=\s]*(?P<tp>[0-9]*[.,]?[0-9]+)\s*"
        r".*?"
        r"(?:SL|Stop\s*Loss|Stop)\s*[-:=\s]*(?P<sl>[0-9]*[.,]?[0-9]+)\s*"
        r".*?"
        r"(?:Time(?:frame)?|TF|Duration)[:\s]*(?P<tf>[0-9]+)",
        re.IGNORECASE | re.DOTALL
    )

# Lista wszystkich patterns do testowania (kolejność ma znaczenie - bardziej specyficzne pierwsze)
SIGNAL_PATTERNS = [
    SignalPatterns.MULTI_TP_PATTERN,  # Najpierw wielokrotne TP
    SignalPatterns.FORMATTED_PATTERN,  # Potem z emoji
    SignalPatterns.MAIN_PATTERN,      # Główny pattern
    SignalPatterns.SIMPLE_PATTERN     # Na końcu prosty (bez timeframe)
]

def normalize_number(value: str) -> float:
    """Normalizuje różne formaty liczb (przecinek/kropka jako separator dziesiętny)."""
    if not value:
        raise ValueError("Pusta wartość")

    # Zamień przecinek na kropkę
    normalized = value.replace(',', '.')

    # Usuń spacje i inne znaki
    normalized = re.sub(r'[^\d.]', '', normalized)

    if not normalized:
        raise ValueError(f"Nie można sparsować liczby: {value}")

    return float(normalized)

def normalize_pair(pair: str) -> str:
    """Normalizuje nazwę pary tradingowej."""
    if not pair:
        raise ValueError("Pusta nazwa pary")

    # Usuń znaki specjalne i konwertuj na wielkie litery
    pair = pair.upper().strip()
    pair = re.sub(r'[^A-Z0-9]', '', pair)

    # Usuń sufiks PERP jeśli istnieje
    if pair.endswith('PERP'):
        pair = pair[:-4]

    # Usuń sufiks .P jeśli istnieje
    if pair.endswith('P'):
        pair = pair[:-1]

    # Dodaj USDT jeśli para nie kończy się na znaną walutę quote
    known_quotes = ['USDT', 'USDC', 'BTC', 'ETH', 'BNB']
    has_quote = any(pair.endswith(quote) for quote in known_quotes)

    if not has_quote:
        pair += 'USDT'

    return pair

def normalize_side(side: str) -> str:
    """Normalizuje kierunek pozycji."""
    side = side.upper().strip()
    if side in ['LONG']:
        return 'BUY'
    elif side in ['SHORT']:
        return 'SELL'
    elif side in ['BUY', 'SELL']:
        return side
    else:
        raise ValueError(f"Nieznany kierunek pozycji: {side}")

def validate_signal_logic(side: str, entry: float, tp: float, sl: float) -> list:
    """Waliduje logikę sygnału i zwraca listę ostrzeżeń."""
    warnings = []

    if side == 'BUY':
        if tp <= entry:
            warnings.append(f"TP ({tp}) powinno być wyższe niż Entry ({entry}) dla pozycji BUY")
        if sl >= entry:
            warnings.append(f"SL ({sl}) powinno być niższe niż Entry ({entry}) dla pozycji BUY")
    elif side == 'SELL':
        if tp >= entry:
            warnings.append(f"TP ({tp}) powinno być niższe niż Entry ({entry}) dla pozycji SELL")
        if sl <= entry:
            warnings.append(f"SL ({sl}) powinno być wyższe niż Entry ({entry}) dla pozycji SELL")

    # Sprawdź czy różnice nie są zbyt małe (mniej niż 0.1%)
    tp_diff = abs(tp - entry) / entry * 100
    sl_diff = abs(sl - entry) / entry * 100

    if tp_diff < 0.1:
        warnings.append(f"TP zbyt blisko Entry (różnica: {tp_diff:.2f}%)")
    if sl_diff < 0.1:
        warnings.append(f"SL zbyt blisko Entry (różnica: {sl_diff:.2f}%)")

    # Sprawdź czy risk/reward ratio nie jest zbyt niekorzystny
    if side == 'BUY':
        risk = entry - sl
        reward = tp - entry
    else:
        risk = sl - entry
        reward = entry - tp

    if risk > 0 and reward > 0:
        rr_ratio = reward / risk
        if rr_ratio < 0.5:
            warnings.append(f"Niekorzystny stosunek risk/reward: {rr_ratio:.2f}")

    return warnings

def parse_signal_advanced(message_content: str) -> Optional[dict]:
    """Zaawansowane parsowanie sygnału z obsługą różnych formatów."""

    for pattern in SIGNAL_PATTERNS:
        match = pattern.search(message_content)
        if match:
            try:
                data = match.groupdict()

                # Normalizacja danych
                side = normalize_side(data['side'])
                pair = normalize_pair(data['pair'])
                entry = normalize_number(data['entry'])

                # Obsługa wielokrotnych TP
                if 'tp1' in data and data['tp1']:
                    tp = normalize_number(data['tp1'])  # Używamy pierwszego TP
                elif 'tp' in data and data['tp']:
                    tp = normalize_number(data['tp'])
                else:
                    raise ValueError("Nie znaleziono TP w sygnale")

                sl = normalize_number(data['sl'])

                # Timeframe wykresu (dla informacji) vs ważność sygnału
                chart_timeframe = None
                if 'tf' in data and data['tf']:
                    chart_timeframe = int(data['tf'])  # TF wykresu (1min, 5min, etc.)

                # Ważność sygnału - zawsze 48 godzin niezależnie od TF wykresu
                signal_validity_minutes = SIGNAL_VALIDITY_HOURS * 60  # 48h = 2880 minut

                # Walidacja logiki sygnału
                warnings = validate_signal_logic(side, entry, tp, sl)

                signal_data = {
                    'pair': pair,
                    'side': side,
                    'entry': entry,
                    'tp': tp,
                    'sl': sl,
                    'timeframe_min': signal_validity_minutes,  # Ważność sygnału (48h)
                    'chart_timeframe': chart_timeframe,  # TF wykresu (1min, 5min, etc.)
                    'warnings': warnings,
                    'pattern_used': pattern.pattern[:50] + '...'  # Dla debugowania
                }

                # Dodaj dodatkowe TP jeśli istnieją
                if 'tp2' in data and data['tp2']:
                    signal_data['tp2'] = normalize_number(data['tp2'])
                if 'tp3' in data and data['tp3']:
                    signal_data['tp3'] = normalize_number(data['tp3'])

                return signal_data

            except (ValueError, KeyError) as e:
                logger.debug(f"Błąd parsowania z pattern {pattern.pattern[:30]}...: {e}")
                continue

    return None

@bot.event
async def on_ready():
    logger.info(f'Logged in as {bot.user} (ID: {bot.user.id})')
    logger.info(f'Monitoring channel: {CHANNEL_ID}')
    logger.info(f'Bot messages allowed: {ALLOW_BOT_MESSAGES}')
    if ALLOW_BOT_MESSAGES:
        if BOT_WHITELIST:
            logger.info(f'Bot whitelist: {", ".join(BOT_WHITELIST)}')
        else:
            logger.info('Bot whitelist: All bots allowed')
    logger.info(f'Signal validity: {SIGNAL_VALIDITY_HOURS} hours')

    if not monitor_signals.is_running():
        monitor_signals.start()

@bot.event
async def on_message(message):
    """Obsługa nowych wiadomości z kanału Discord."""
    # Sprawdź czy wiadomość jest z właściwego kanału
    if str(message.channel.id) != CHANNEL_ID:
        return

    # Sprawdź czy wiadomość jest od bota i czy boty są dozwolone
    if message.author.bot:
        if not is_bot_allowed(message.author.id):
            logger.debug(f'Pominięto wiadomość od bota {message.author.id} (nie na whiteliście lub boty wyłączone)')
            return
        else:
            logger.info(f'Przetwarzanie wiadomości od dozwolonego bota: {message.author.name} (ID: {message.author.id})')

    # Sprawdź czy wiadomość nie jest zbyt stara (tylko dla nowych wiadomości, nie historycznych)
    message_age = datetime.now(timezone.utc) - message.created_at.replace(tzinfo=timezone.utc)
    if message_age > timedelta(hours=SIGNAL_VALIDITY_HOURS):
        logger.debug(f'Pominięto starą wiadomość {message.id} (wiek: {message_age})')
        return

    # Spróbuj sparsować sygnał używając zaawansowanego parsera
    signal_data = parse_signal_advanced(message.content)

    if signal_data:
        # Wyloguj ostrzeżenia jeśli istnieją
        if signal_data.get('warnings'):
            for warning in signal_data['warnings']:
                logger.warning(f'Signal {message.id} validation warning: {warning}')

        ts = datetime.now(timezone.utc)
        try:
            cursor.execute(
                "INSERT INTO signals (message_id, pair, side, entry, tp, sl, timestamp, timeframe_min, status)"
                " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                (str(message.id), signal_data['pair'], signal_data['side'],
                 signal_data['entry'], signal_data['tp'], signal_data['sl'],
                 ts, signal_data['timeframe_min'], 'NEW')
            )
            conn.commit()

            # Loguj informacje o sygnale
            chart_tf_info = f' (Chart TF: {signal_data["chart_timeframe"]}m)' if signal_data["chart_timeframe"] else ''
            log_msg = (f'Registered signal {message.id}: {signal_data["side"]} {signal_data["pair"]} '
                      f'@ {signal_data["entry"]} TP {signal_data["tp"]} SL {signal_data["sl"]} '
                      f'Validity: {signal_data["timeframe_min"]}m{chart_tf_info}')

            if signal_data.get('warnings'):
                log_msg += f' (Warnings: {len(signal_data["warnings"])})'

            logger.info(log_msg)

            # Powiadom dashboard o nowym sygnale
            dashboard_data = {
                "id": str(message.id),
                "pair": signal_data['pair'],
                "side": signal_data['side'],
                "entry": signal_data['entry'],
                "tp": signal_data['tp'],
                "sl": signal_data['sl'],
                "timeframe_min": signal_data['timeframe_min']
            }

            # Dodaj dodatkowe TP jeśli istnieją
            if 'tp2' in signal_data:
                dashboard_data['tp2'] = signal_data['tp2']
            if 'tp3' in signal_data:
                dashboard_data['tp3'] = signal_data['tp3']

            notify_dashboard("new_signal", dashboard_data)

        except sqlite3.IntegrityError:
            logger.debug(f'Signal {message.id} already exists')
        except Exception as e:
            logger.error(f'Error saving signal {message.id}: {e}')
    else:
        # Sprawdź czy wiadomość może zawierać sygnał (dla debugowania)
        signal_keywords = ['buy', 'sell', 'long', 'short', 'entry', 'tp', 'sl']
        content_lower = message.content.lower()

        if any(keyword in content_lower for keyword in signal_keywords):
            logger.debug(f'Message {message.id} contains signal keywords but failed to parse: {message.content[:100]}...')

    await bot.process_commands(message)

@tasks.loop(seconds=PRICE_CHECK_INTERVAL)
async def monitor_signals():
    """Monitoruj otwarte sygnały i sprawdzaj warunki zamknięcia."""
    now = datetime.now(timezone.utc)

    try:
        cursor.execute(
            "SELECT id, pair, side, entry, tp, sl, timestamp, timeframe_min"
            " FROM signals WHERE status IN ('NEW', 'ENTRY_HIT')"
        )
        open_signals = cursor.fetchall()

        if not open_signals:
            logger.debug("Brak otwartych sygnałów do monitorowania")
            return

        logger.debug(f"Monitorowanie {len(open_signals)} otwartych sygnałów")

        for row in open_signals:
            sig_id, pair, side, entry, tp, sl, ts, tf = row

            # Bezpieczne parsowanie timestamp
            ts = parse_timestamp_safely(ts)
            if ts is None:
                logger.warning(f'Pominięto sygnał {sig_id} - nieprawidłowy timestamp')
                continue

            # Sprawdź czy para nadal istnieje na giełdzie
            if not validate_pair_exists(pair):
                logger.warning(f'Para {pair} nie istnieje na Bybit - pomijam sygnał {sig_id}')
                continue

            # Pobierz aktualną cenę z retry
            price = get_price_with_retry(pair)
            if price is None:
                logger.error(f'Nie udało się pobrać ceny dla {pair} - pomijam sygnał {sig_id}')
                continue

            logger.debug(f'Signal {sig_id} ({pair}): current price {price}, entry {entry}, tp {tp}, sl {sl}')

            # Pobierz aktualny status sygnału
            cursor.execute("SELECT status FROM signals WHERE id=?", (sig_id,))
            current_status = cursor.fetchone()[0]

            hit = None
            exit_price = price
            new_status = current_status

            # Sprawdź wygaśnięcie timeframe (48h)
            if now >= ts + timedelta(minutes=tf):
                hit = 'EXPIRED'
                exit_price = price
                new_status = 'EXPIRED'
            else:
                # Sprawdź czy sygnał osiągnął poziom entry
                entry_hit = False
                if side == 'BUY':
                    entry_hit = price <= entry  # Dla BUY, entry hit gdy cena spadnie do lub poniżej entry
                else:  # SELL
                    entry_hit = price >= entry  # Dla SELL, entry hit gdy cena wzrośnie do lub powyżej entry

                if current_status == 'NEW' and entry_hit:
                    # Sygnał osiągnął poziom entry
                    new_status = 'ENTRY_HIT'
                    logger.info(f'Signal {sig_id} ({pair}) entry hit at {price:.6f}')

                    # Aktualizuj status bez zamykania sygnału
                    cursor.execute(
                        "UPDATE signals SET status=? WHERE id=?",
                        (new_status, sig_id)
                    )
                    conn.commit()

                    # Powiadom dashboard o zmianie statusu
                    notify_dashboard("signal_update", {
                        "id": sig_id,
                        "status": new_status,
                        "pair": pair,
                        "side": side
                    })
                    continue

                # Sprawdź warunki TP/SL tylko jeśli sygnał osiągnął entry
                if current_status == 'ENTRY_HIT':
                    if side == 'BUY':
                        if price >= tp:
                            hit = 'TP_HIT'
                            exit_price = tp
                        elif price <= sl:
                            hit = 'SL_HIT'
                            exit_price = sl
                    else:  # SELL
                        if price <= tp:
                            hit = 'TP_HIT'
                            exit_price = tp
                        elif price >= sl:
                            hit = 'SL_HIT'
                            exit_price = sl

            # Zamknij sygnał jeśli warunek został spełniony
            if hit:
                pnl = (exit_price - entry) / entry * (1 if side == 'BUY' else -1)

                try:
                    cursor.execute(
                        "UPDATE signals SET status=?, close_timestamp=?, exit_price=?, pnl=? WHERE id=?",
                        (hit, now, exit_price, pnl, sig_id)
                    )
                    conn.commit()

                    logger.info(f'Closed signal {sig_id} ({pair}) by {hit} at {exit_price:.6f}, PnL: {pnl:.2%}')

                    # Powiadom dashboard o zamknięciu sygnału
                    notify_dashboard("signal_update", {
                        "id": sig_id,
                        "status": hit,
                        "exit_price": exit_price,
                        "pnl": pnl,
                        "pair": pair,
                        "side": side
                    })

                except Exception as e:
                    logger.error(f'Error updating signal {sig_id}: {e}')

    except Exception as e:
        logger.error(f'Error in monitor_signals: {e}')

@bot.command(name='stats')
async def stats(ctx):
    cursor.execute(
        "SELECT COUNT(*),"
        " SUM(CASE WHEN pnl>0 THEN 1 ELSE 0 END),"
        " AVG(pnl) FROM signals WHERE status IN ('TP_HIT', 'SL_HIT', 'EXPIRED')"
    )
    total, wins, avg_pnl = cursor.fetchone()
    if total:
        win_rate = wins / total * 100
        msg = (
            f"📊 **Stats**\n"
            f"Total: {total}\n"
            f"Winners: {wins} ({win_rate:.1f}%)\n"
            f"Avg. PnL: {avg_pnl:.2%}"
        )
    else:
        msg = "Brak zamkniętych sygnałów."
    await ctx.send(msg)

if __name__ == '__main__':
    try:
        logger.info('Uruchamianie Discord Bybit Signal Monitor...')
        bot.run(DISCORD_TOKEN)
    except KeyboardInterrupt:
        logger.info('Zamykanie aplikacji...')
    except Exception as e:
        logger.error(f'Błąd uruchomienia: {e}')
        logger.info('Sprawdź konfigurację w pliku .env')
        raise

