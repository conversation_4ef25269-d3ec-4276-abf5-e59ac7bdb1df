#!/usr/bin/env python3
"""
Sprawdź status sygnałów w bazie danych.
"""

import sqlite3
from datetime import datetime, timezone

def check_database():
    """Sprawdź status sygnałów w bazie danych."""
    
    conn = sqlite3.connect('signals.db')
    cursor = conn.cursor()
    
    # Status sygnałów
    cursor.execute('SELECT status, COUNT(*) FROM signals GROUP BY status')
    print('Status sygnałów:')
    for status, count in cursor.fetchall():
        print(f'  {status}: {count}')
    
    # Sygnały z TF 1 minuta
    cursor.execute('SELECT COUNT(*) FROM signals WHERE timeframe_min = 1')
    tf_1min_count = cursor.fetchone()[0]
    print(f'\nSygnały z TF 1 minuta: {tf_1min_count}')
    
    # Ostatnie sygnały z timeout
    cursor.execute('''
        SELECT pair, side, entry, tp, sl, timestamp, timeframe_min, status 
        FROM signals 
        WHERE status = "timeout" 
        ORDER BY timestamp DESC 
        LIMIT 5
    ''')
    print('\nOstatnie 5 sygnałów z timeout:')
    for pair, side, entry, tp, sl, ts, tf, status in cursor.fetchall():
        print(f'  {pair} {side} @ {entry} TF:{tf}m - {status}')
    
    # Otwarte sygnały
    cursor.execute('''
        SELECT pair, side, entry, tp, sl, timestamp, timeframe_min 
        FROM signals 
        WHERE status = "open" 
        ORDER BY timestamp DESC
    ''')
    open_signals = cursor.fetchall()
    print(f'\nOtwarte sygnały: {len(open_signals)}')
    for pair, side, entry, tp, sl, ts, tf in open_signals:
        print(f'  {pair} {side} @ {entry} TF:{tf}m')
    
    # Sprawdź czy są sygnały z bardzo krótkim timeframe
    cursor.execute('SELECT MIN(timeframe_min), MAX(timeframe_min), AVG(timeframe_min) FROM signals')
    min_tf, max_tf, avg_tf = cursor.fetchone()
    print(f'\nTimeframe stats:')
    print(f'  Min: {min_tf}m, Max: {max_tf}m, Avg: {avg_tf:.1f}m')
    
    conn.close()

if __name__ == '__main__':
    check_database()
