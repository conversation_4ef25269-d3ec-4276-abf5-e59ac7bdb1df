#!/usr/bin/env python3
"""
Dashboard Web dla Discord Bybit Signal Monitor

Aplikacja Flask do wyświetlania statystyk i analizy sygnałów tradingowych.
Zawiera funkcjonalność importu historycznych sygnałów z Discord.

Autor: AI Assistant
Data: 2025-06-14
"""

import os
import sqlite3
import json
import math
from datetime import datetime, timedelta, timezone
from flask import Flask, render_template, jsonify, request
from flask_socketio import SocketIO, emit
import pandas as pd
import numpy as np
from dotenv import load_dotenv

# Ładowanie konfiguracji
load_dotenv()
DB_PATH = os.getenv('DB_PATH', 'signals.db')
DISCORD_TOKEN = os.getenv('DISCORD_TOKEN')
CHANNEL_ID = os.getenv('DISCORD_CHANNEL_ID')

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['JSON_SORT_KEYS'] = False
socketio = SocketIO(app, cors_allowed_origins="*")

def sanitize_for_json(obj):
    """
    Sanityzuje obiekt do bezpiecznego formatu JSON, zastępując NaN i Infinity.

    Args:
        obj: Obiekt do sanityzacji (może być dict, list, float, int, etc.)

    Returns:
        Obiekt z zastąpionymi problematycznymi wartościami
    """
    if isinstance(obj, dict):
        return {key: sanitize_for_json(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [sanitize_for_json(item) for item in obj]
    elif isinstance(obj, float):
        if math.isnan(obj):
            return 0.0  # Zastąp NaN zerem
        elif math.isinf(obj):
            if obj > 0:
                return 999999.0  # Zastąp +Infinity dużą liczbą
            else:
                return -999999.0  # Zastąp -Infinity dużą ujemną liczbą
        else:
            return obj
    elif isinstance(obj, np.floating):
        if np.isnan(obj):
            return 0.0
        elif np.isinf(obj):
            if obj > 0:
                return 999999.0
            else:
                return -999999.0
        else:
            return float(obj)
    elif isinstance(obj, (np.integer, np.int64, np.int32)):
        return int(obj)
    else:
        return obj

class SignalDatabase:
    """Klasa do zarządzania bazą danych sygnałów."""

    def __init__(self, db_path=DB_PATH):
        self.db_path = db_path

    def get_connection(self):
        """Uzyskaj połączenie z bazą danych."""
        return sqlite3.connect(self.db_path)

    def get_all_signals(self, limit=None):
        """Pobierz wszystkie sygnały z bazy."""
        conn = self.get_connection()
        query = """
        SELECT id, message_id, pair, side, entry, tp, sl,
               timestamp, timeframe_min, status, close_timestamp,
               exit_price, pnl
        FROM signals
        ORDER BY timestamp DESC
        """
        if limit:
            query += f" LIMIT {limit}"

        df = pd.read_sql_query(query, conn)
        conn.close()
        return df

    def get_statistics(self, days_filter=None):
        """Oblicz rozszerzone statystyki sygnałów."""
        conn = self.get_connection()

        # Filtr czasowy
        time_filter = ""
        if days_filter:
            time_filter = f"AND timestamp >= datetime('now', '-{days_filter} days')"

        # Podstawowe statystyki
        stats_query = f"""
        SELECT
            COUNT(*) as total_signals,
            COUNT(CASE WHEN status NOT IN ('NEW', 'ENTRY_HIT') THEN 1 END) as closed_signals,
            COUNT(CASE WHEN pnl > 0 THEN 1 END) as winning_signals,
            COUNT(CASE WHEN status = 'TP_HIT' THEN 1 END) as tp_hits,
            COUNT(CASE WHEN status = 'SL_HIT' THEN 1 END) as sl_hits,
            COUNT(CASE WHEN status = 'EXPIRED' THEN 1 END) as expired_signals,
            COUNT(CASE WHEN status = 'NEW' THEN 1 END) as new_signals,
            COUNT(CASE WHEN status = 'ENTRY_HIT' THEN 1 END) as entry_hit_signals,
            AVG(CASE WHEN pnl IS NOT NULL THEN pnl END) as avg_pnl,
            MAX(CASE WHEN pnl IS NOT NULL THEN pnl END) as max_pnl,
            MIN(CASE WHEN pnl IS NOT NULL THEN pnl END) as min_pnl,
            SUM(CASE WHEN pnl IS NOT NULL THEN pnl END) as total_pnl
        FROM signals
        WHERE 1=1 {time_filter}
        """

        cursor = conn.cursor()
        cursor.execute(stats_query)
        stats = cursor.fetchone()

        # Zaawansowane metryki
        advanced_stats = self._calculate_advanced_metrics(conn, time_filter)

        # Statystyki per para
        pair_stats_query = f"""
        SELECT pair,
               COUNT(*) as count,
               COUNT(CASE WHEN pnl > 0 THEN 1 END) as wins,
               AVG(CASE WHEN pnl IS NOT NULL THEN pnl END) as avg_pnl,
               SUM(CASE WHEN pnl IS NOT NULL THEN pnl END) as total_pnl
        FROM signals
        WHERE status != 'open' {time_filter}
        GROUP BY pair
        ORDER BY count DESC
        """

        pair_stats = pd.read_sql_query(pair_stats_query, conn)

        # Statystyki per timeframe
        tf_stats_query = f"""
        SELECT timeframe_min,
               COUNT(*) as count,
               COUNT(CASE WHEN pnl > 0 THEN 1 END) as wins,
               AVG(CASE WHEN pnl IS NOT NULL THEN pnl END) as avg_pnl
        FROM signals
        WHERE status != 'open' {time_filter}
        GROUP BY timeframe_min
        ORDER BY timeframe_min
        """

        tf_stats = pd.read_sql_query(tf_stats_query, conn)
        conn.close()

        return {
            'total_signals': stats[0],
            'closed_signals': stats[1],
            'winning_signals': stats[2],
            'tp_hits': stats[3],
            'sl_hits': stats[4],
            'timeouts': stats[5],
            'avg_pnl': stats[6] or 0,
            'max_pnl': stats[7] or 0,
            'min_pnl': stats[8] or 0,
            'total_pnl': stats[9] or 0,
            'win_rate': (stats[2] / stats[1] * 100) if stats[1] > 0 else 0,
            'pair_stats': pair_stats.to_dict('records'),
            'timeframe_stats': tf_stats.to_dict('records'),
            **advanced_stats
        }

    def _calculate_advanced_metrics(self, conn, time_filter=""):
        """Oblicz zaawansowane metryki wydajności."""
        # Pobierz wszystkie zamknięte sygnały z PnL
        pnl_query = f"""
        SELECT pnl, close_timestamp
        FROM signals
        WHERE pnl IS NOT NULL AND status != 'open' {time_filter}
        ORDER BY close_timestamp
        """

        df = pd.read_sql_query(pnl_query, conn)

        if df.empty:
            return {
                'sharpe_ratio': 0,
                'max_drawdown': 0,
                'max_consecutive_wins': 0,
                'max_consecutive_losses': 0,
                'profit_factor': 0,
                'avg_win': 0,
                'avg_loss': 0
            }

        pnl_series = df['pnl']

        # Sharpe Ratio (uproszczony - bez risk-free rate)
        pnl_std = pnl_series.std()
        if pnl_std > 0 and not pd.isna(pnl_std):
            sharpe_ratio = pnl_series.mean() / pnl_std
            if pd.isna(sharpe_ratio):
                sharpe_ratio = 0.0
        else:
            sharpe_ratio = 0.0

        # Max Drawdown
        cumulative_pnl = pnl_series.cumsum()
        running_max = cumulative_pnl.expanding().max()
        drawdown = (cumulative_pnl - running_max)
        max_drawdown_val = drawdown.min()
        max_drawdown = abs(max_drawdown_val) if not drawdown.empty and not pd.isna(max_drawdown_val) else 0.0

        # Consecutive wins/losses
        wins_losses = (pnl_series > 0).astype(int)
        consecutive_wins = self._max_consecutive(wins_losses, 1)
        consecutive_losses = self._max_consecutive(wins_losses, 0)

        # Profit Factor - bezpieczne obliczenie
        total_wins = pnl_series[pnl_series > 0].sum()
        total_losses = abs(pnl_series[pnl_series < 0].sum())

        if pd.isna(total_wins):
            total_wins = 0.0
        if pd.isna(total_losses):
            total_losses = 0.0

        if total_losses > 0:
            profit_factor = total_wins / total_losses
            if pd.isna(profit_factor) or math.isinf(profit_factor):
                profit_factor = 999999.0  # Zastąp problematyczne wartości
        else:
            profit_factor = 999999.0 if total_wins > 0 else 0.0

        # Średnie zyski/straty - bezpieczne obliczenie
        if (pnl_series > 0).any():
            avg_win = pnl_series[pnl_series > 0].mean()
            if pd.isna(avg_win):
                avg_win = 0.0
        else:
            avg_win = 0.0

        if (pnl_series < 0).any():
            avg_loss = pnl_series[pnl_series < 0].mean()
            if pd.isna(avg_loss):
                avg_loss = 0.0
        else:
            avg_loss = 0.0

        return {
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'max_consecutive_wins': consecutive_wins,
            'max_consecutive_losses': consecutive_losses,
            'profit_factor': profit_factor,
            'avg_win': avg_win,
            'avg_loss': avg_loss
        }

    def _max_consecutive(self, series, value):
        """Oblicz maksymalną liczbę kolejnych wystąpień wartości."""
        if series.empty:
            return 0

        consecutive = 0
        max_consecutive = 0

        for val in series:
            if val == value:
                consecutive += 1
                max_consecutive = max(max_consecutive, consecutive)
            else:
                consecutive = 0

        return max_consecutive

    def get_pnl_over_time(self, days_filter=None):
        """Pobierz PnL w czasie dla wykresów."""
        conn = self.get_connection()

        time_filter = ""
        if days_filter:
            time_filter = f"AND close_timestamp >= datetime('now', '-{days_filter} days')"

        query = f"""
        SELECT DATE(close_timestamp) as date,
               SUM(pnl) as daily_pnl,
               COUNT(*) as signals_count
        FROM signals
        WHERE status != 'open' AND close_timestamp IS NOT NULL {time_filter}
        GROUP BY DATE(close_timestamp)
        ORDER BY date
        """

        df = pd.read_sql_query(query, conn)
        conn.close()

        if not df.empty:
            df['cumulative_pnl'] = df['daily_pnl'].cumsum()

        return df.to_dict('records')

    def get_pnl_distribution(self):
        """Pobierz rozkład PnL dla histogramu."""
        conn = self.get_connection()
        query = """
        SELECT pnl
        FROM signals
        WHERE pnl IS NOT NULL AND status != 'open'
        ORDER BY pnl
        """

        df = pd.read_sql_query(query, conn)
        conn.close()

        if df.empty:
            return []

        # Utwórz histogram
        pnl_values = df['pnl'].values
        bins = 20  # Liczba przedziałów
        hist, bin_edges = pd.cut(pnl_values, bins=bins, retbins=True)

        distribution = []
        for i in range(len(bin_edges) - 1):
            count = ((pnl_values >= bin_edges[i]) & (pnl_values < bin_edges[i + 1])).sum()
            if i == len(bin_edges) - 2:  # Ostatni przedział - włącz górną granicę
                count = ((pnl_values >= bin_edges[i]) & (pnl_values <= bin_edges[i + 1])).sum()

            # Bezpieczne obliczenie procentów
            percentage = (count / len(pnl_values)) * 100 if len(pnl_values) > 0 else 0.0
            if pd.isna(percentage):
                percentage = 0.0

            distribution.append({
                'range_start': float(bin_edges[i]) if not pd.isna(bin_edges[i]) else 0.0,
                'range_end': float(bin_edges[i + 1]) if not pd.isna(bin_edges[i + 1]) else 0.0,
                'count': int(count),
                'percentage': percentage
            })

        return distribution

db = SignalDatabase()

@app.route('/')
def dashboard():
    """Główna strona dashboard."""
    return render_template('dashboard.html')

@app.route('/api/signals')
def api_signals():
    """API endpoint dla sygnałów."""
    limit = request.args.get('limit', type=int)
    status = request.args.get('status')
    pair = request.args.get('pair')

    df = db.get_all_signals(limit)

    # Filtrowanie
    if status and status != 'all':
        df = df[df['status'] == status]
    if pair and pair != 'all':
        df = df[df['pair'] == pair]

    # Konwersja do JSON-friendly format
    signals = df.to_dict('records')
    for signal in signals:
        if signal['timestamp']:
            signal['timestamp'] = pd.to_datetime(signal['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
        if signal['close_timestamp']:
            signal['close_timestamp'] = pd.to_datetime(signal['close_timestamp']).strftime('%Y-%m-%d %H:%M:%S')
        if signal['pnl']:
            signal['pnl_percent'] = f"{signal['pnl']:.2%}"

    # Sanityzacja danych przed wysłaniem
    sanitized_signals = sanitize_for_json(signals)
    return jsonify(sanitized_signals)

@app.route('/api/statistics')
def api_statistics():
    """API endpoint dla statystyk."""
    days_filter = request.args.get('days', type=int)
    stats = db.get_statistics(days_filter)
    sanitized_stats = sanitize_for_json(stats)
    return jsonify(sanitized_stats)

@app.route('/api/performance-metrics')
def api_performance_metrics():
    """API endpoint dla zaawansowanych metryk wydajności."""
    days_filter = request.args.get('days', type=int)
    stats = db.get_statistics(days_filter)

    # Wyciągnij tylko metryki wydajności
    performance_metrics = {
        'sharpe_ratio': stats.get('sharpe_ratio', 0),
        'max_drawdown': stats.get('max_drawdown', 0),
        'max_consecutive_wins': stats.get('max_consecutive_wins', 0),
        'max_consecutive_losses': stats.get('max_consecutive_losses', 0),
        'profit_factor': stats.get('profit_factor', 0),
        'avg_win': stats.get('avg_win', 0),
        'avg_loss': stats.get('avg_loss', 0),
        'total_pnl': stats.get('total_pnl', 0)
    }

    # Sanityzacja metryk wydajności
    sanitized_metrics = sanitize_for_json(performance_metrics)
    return jsonify(sanitized_metrics)

@app.route('/api/heatmap-data')
def api_heatmap_data():
    """API endpoint dla danych heatmapy wydajności."""
    conn = db.get_connection()

    # Pobierz dane dla heatmapy (para vs dzień)
    heatmap_query = """
    SELECT
        pair,
        DATE(close_timestamp) as date,
        SUM(pnl) as daily_pnl,
        COUNT(*) as signals_count
    FROM signals
    WHERE status != 'open' AND close_timestamp IS NOT NULL
    GROUP BY pair, DATE(close_timestamp)
    ORDER BY date DESC, pair
    """

    df = pd.read_sql_query(heatmap_query, conn)
    conn.close()

    # Sanityzacja danych heatmapy
    heatmap_data = sanitize_for_json(df.to_dict('records'))
    return jsonify(heatmap_data)

@app.route('/api/pnl-chart')
def api_pnl_chart():
    """API endpoint dla danych wykresu PnL."""
    days_filter = request.args.get('days', type=int)
    data = db.get_pnl_over_time(days_filter)
    sanitized_data = sanitize_for_json(data)
    return jsonify(sanitized_data)

@app.route('/api/pnl-distribution')
def api_pnl_distribution():
    """API endpoint dla rozkładu PnL."""
    data = db.get_pnl_distribution()
    sanitized_data = sanitize_for_json(data)
    return jsonify(sanitized_data)

@app.route('/api/pairs')
def api_pairs():
    """API endpoint dla listy par."""
    conn = db.get_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT DISTINCT pair FROM signals ORDER BY pair")
    pairs = [row[0] for row in cursor.fetchall()]
    conn.close()
    return jsonify(pairs)

@app.route('/api/webhook', methods=['POST'])
def webhook():
    """Webhook do otrzymywania powiadomień od bota."""
    try:
        data = request.get_json()
        event_type = data.get('type')
        event_data = data.get('data')

        if event_type == 'new_signal':
            broadcast_new_signal(event_data)
        elif event_type == 'signal_update':
            broadcast_signal_update(event_data)

        return jsonify({"status": "success"})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 400

@socketio.on('connect')
def handle_connect():
    """Obsługa połączenia WebSocket."""
    print('Client connected')
    emit('status', {'msg': 'Connected to dashboard'})

@socketio.on('disconnect')
def handle_disconnect():
    """Obsługa rozłączenia WebSocket."""
    print('Client disconnected')

def broadcast_new_signal(signal_data):
    """Wyślij nowy sygnał do wszystkich połączonych klientów."""
    sanitized_data = sanitize_for_json(signal_data)
    socketio.emit('new_signal', sanitized_data)

def broadcast_signal_update(signal_data):
    """Wyślij aktualizację sygnału do wszystkich połączonych klientów."""
    sanitized_data = sanitize_for_json(signal_data)
    socketio.emit('signal_update', sanitized_data)

if __name__ == '__main__':
    print("🚀 Uruchamianie Dashboard Web...")
    print("📊 Dashboard dostępny pod adresem: http://localhost:5000")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
